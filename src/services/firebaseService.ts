import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Product, Sale, Customer, Supplier, Debt, DebtPayment } from '@/types';

// Collections names
const COLLECTIONS = {
  PRODUCTS: 'products',
  SALES: 'sales',
  CUSTOMERS: 'customers',
  SUPPLIERS: 'suppliers',
  DEBTS: 'debts',
  DEBT_PAYMENTS: 'debtPayments'
};

// Helper function to convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any) => {
  if (timestamp && timestamp.toDate) {
    return timestamp.toDate();
  }
  return timestamp instanceof Date ? timestamp : new Date(timestamp);
};

// Products Service
export const productsService = {
  // Get all products
  async getAll(): Promise<Product[]> {
    const querySnapshot = await getDocs(collection(db, COLLECTIONS.PRODUCTS));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: convertTimestamp(doc.data().createdAt),
      updatedAt: convertTimestamp(doc.data().updatedAt)
    })) as Product[];
  },

  // Add new product
  async add(product: Omit<Product, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.PRODUCTS), {
      ...product,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    return docRef.id;
  },

  // Update product
  async update(id: string, updates: Partial<Product>): Promise<void> {
    const docRef = doc(db, COLLECTIONS.PRODUCTS, id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  },

  // Delete product
  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, COLLECTIONS.PRODUCTS, id));
  },

  // Listen to products changes
  onSnapshot(callback: (products: Product[]) => void) {
    return onSnapshot(collection(db, COLLECTIONS.PRODUCTS), (snapshot) => {
      const products = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt),
        updatedAt: convertTimestamp(doc.data().updatedAt)
      })) as Product[];
      callback(products);
    });
  }
};

// Sales Service
export const salesService = {
  // Get all sales
  async getAll(): Promise<Sale[]> {
    const querySnapshot = await getDocs(
      query(collection(db, COLLECTIONS.SALES), orderBy('date', 'desc'))
    );
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: convertTimestamp(doc.data().date)
    })) as Sale[];
  },

  // Add new sale
  async add(sale: Omit<Sale, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.SALES), {
      ...sale,
      date: Timestamp.fromDate(sale.date)
    });
    return docRef.id;
  },

  // Listen to sales changes
  onSnapshot(callback: (sales: Sale[]) => void) {
    return onSnapshot(
      query(collection(db, COLLECTIONS.SALES), orderBy('date', 'desc')),
      (snapshot) => {
        const sales = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          date: convertTimestamp(doc.data().date)
        })) as Sale[];
        callback(sales);
      }
    );
  }
};

// Customers Service
export const customersService = {
  async getAll(): Promise<Customer[]> {
    const querySnapshot = await getDocs(collection(db, COLLECTIONS.CUSTOMERS));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: convertTimestamp(doc.data().createdAt)
    })) as Customer[];
  },

  async add(customer: Omit<Customer, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.CUSTOMERS), {
      ...customer,
      createdAt: Timestamp.now()
    });
    return docRef.id;
  },

  async update(id: string, updates: Partial<Customer>): Promise<void> {
    const docRef = doc(db, COLLECTIONS.CUSTOMERS, id);
    await updateDoc(docRef, updates);
  },

  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, COLLECTIONS.CUSTOMERS, id));
  },

  onSnapshot(callback: (customers: Customer[]) => void) {
    return onSnapshot(collection(db, COLLECTIONS.CUSTOMERS), (snapshot) => {
      const customers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt)
      })) as Customer[];
      callback(customers);
    });
  }
};

// Suppliers Service
export const suppliersService = {
  async getAll(): Promise<Supplier[]> {
    const querySnapshot = await getDocs(collection(db, COLLECTIONS.SUPPLIERS));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: convertTimestamp(doc.data().createdAt)
    })) as Supplier[];
  },

  async add(supplier: Omit<Supplier, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.SUPPLIERS), {
      ...supplier,
      createdAt: Timestamp.now()
    });
    return docRef.id;
  },

  async update(id: string, updates: Partial<Supplier>): Promise<void> {
    const docRef = doc(db, COLLECTIONS.SUPPLIERS, id);
    await updateDoc(docRef, updates);
  },

  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, COLLECTIONS.SUPPLIERS, id));
  },

  onSnapshot(callback: (suppliers: Supplier[]) => void) {
    return onSnapshot(collection(db, COLLECTIONS.SUPPLIERS), (snapshot) => {
      const suppliers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt)
      })) as Supplier[];
      callback(suppliers);
    });
  }
};

// Debts Service
export const debtsService = {
  async getAll(): Promise<Debt[]> {
    const querySnapshot = await getDocs(
      query(collection(db, COLLECTIONS.DEBTS), orderBy('createdAt', 'desc'))
    );
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: convertTimestamp(doc.data().createdAt),
      updatedAt: convertTimestamp(doc.data().updatedAt),
      dueDate: doc.data().dueDate ? convertTimestamp(doc.data().dueDate) : undefined
    })) as Debt[];
  },

  async add(debt: Omit<Debt, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.DEBTS), {
      ...debt,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      dueDate: debt.dueDate ? Timestamp.fromDate(debt.dueDate) : null
    });
    return docRef.id;
  },

  async update(id: string, updates: Partial<Debt>): Promise<void> {
    const docRef = doc(db, COLLECTIONS.DEBTS, id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now(),
      dueDate: updates.dueDate ? Timestamp.fromDate(updates.dueDate) : undefined
    });
  },

  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, COLLECTIONS.DEBTS, id));
  },

  onSnapshot(callback: (debts: Debt[]) => void) {
    return onSnapshot(
      query(collection(db, COLLECTIONS.DEBTS), orderBy('createdAt', 'desc')),
      (snapshot) => {
        const debts = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: convertTimestamp(doc.data().createdAt),
          updatedAt: convertTimestamp(doc.data().updatedAt),
          dueDate: doc.data().dueDate ? convertTimestamp(doc.data().dueDate) : undefined
        })) as Debt[];
        callback(debts);
      }
    );
  }
};

// Debt Payments Service
export const debtPaymentsService = {
  async getAll(): Promise<DebtPayment[]> {
    const querySnapshot = await getDocs(collection(db, COLLECTIONS.DEBT_PAYMENTS));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      paymentDate: convertTimestamp(doc.data().paymentDate),
      createdAt: convertTimestamp(doc.data().createdAt)
    })) as DebtPayment[];
  },

  async add(payment: Omit<DebtPayment, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.DEBT_PAYMENTS), {
      ...payment,
      paymentDate: Timestamp.fromDate(payment.paymentDate),
      createdAt: Timestamp.now()
    });
    return docRef.id;
  },

  async getByDebtId(debtId: string): Promise<DebtPayment[]> {
    const q = query(
      collection(db, COLLECTIONS.DEBT_PAYMENTS),
      where('debtId', '==', debtId)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      paymentDate: convertTimestamp(doc.data().paymentDate),
      createdAt: convertTimestamp(doc.data().createdAt)
    })) as DebtPayment[];
  },

  onSnapshot(callback: (payments: DebtPayment[]) => void) {
    return onSnapshot(collection(db, COLLECTIONS.DEBT_PAYMENTS), (snapshot) => {
      const payments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        paymentDate: convertTimestamp(doc.data().paymentDate),
        createdAt: convertTimestamp(doc.data().createdAt)
      })) as DebtPayment[];
      callback(payments);
    });
  }
};

// Batch operations for better performance
export const batchOperations = {
  async addMultipleProducts(products: Omit<Product, 'id'>[]): Promise<void> {
    const batch = writeBatch(db);
    
    products.forEach((product) => {
      const docRef = doc(collection(db, COLLECTIONS.PRODUCTS));
      batch.set(docRef, {
        ...product,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    });
    
    await batch.commit();
  }
};
