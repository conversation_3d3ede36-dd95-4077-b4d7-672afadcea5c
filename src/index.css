
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 248 250 252;
    --foreground: 15 23 42;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 240 244 248;
    --secondary-foreground: 51 65 85;
    --muted: 248 250 252;
    --muted-foreground: 100 116 139;
    --accent: 16 185 129;
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;
    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-cairo;
    direction: rtl;
  }

  .font-cairo {
    font-family: 'Cairo', sans-serif;
  }
}

@layer utilities {
  .gradient-bg {
    background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
  }
  
  .card-hover {
    transition: all 0.3s ease;
  }
  
  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}
