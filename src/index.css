
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 248 250 252;
    --foreground: 15 23 42;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 240 244 248;
    --secondary-foreground: 51 65 85;
    --muted: 248 250 252;
    --muted-foreground: 100 116 139;
    --accent: 16 185 129;
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;
    --radius: 0.75rem;
  }

  .dark {
    --background: 15 23 42;
    --foreground: 248 250 252;
    --card: 30 41 59;
    --card-foreground: 248 250 252;
    --popover: 30 41 59;
    --popover-foreground: 248 250 252;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 51 65 85;
    --secondary-foreground: 226 232 240;
    --muted: 51 65 85;
    --muted-foreground: 148 163 184;
    --accent: 16 185 129;
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 51 65 85;
    --input: 51 65 85;
    --ring: 59 130 246;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-cairo;
    direction: rtl;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  .font-cairo {
    font-family: 'Cairo', sans-serif;
  }

  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .card-hover {
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }

    body {
      direction: rtl;
      font-family: 'Cairo', sans-serif;
    }
  }
}

@layer utilities {
  .gradient-bg {
    background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
  }
  
  .card-hover {
    transition: all 0.3s ease;
  }
  
  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}
