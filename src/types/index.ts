
export interface Product {
  id: string;
  name: string;
  price: number;
  quantity: number;
  code: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaleItem {
  product: Product;
  quantity: number;
  subtotal: number;
}

export interface Sale {
  id: string;
  items: SaleItem[];
  total: number;
  date: Date;
  invoiceNumber: string;
}

export interface StockAlert {
  product: Product;
  currentQuantity: number;
  minimumThreshold: number;
}
