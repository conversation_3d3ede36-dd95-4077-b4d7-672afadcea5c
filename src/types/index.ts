
export interface Product {
  id: string;
  name: string;
  price: number;
  quantity: number;
  code: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaleItem {
  product: Product;
  quantity: number;
  subtotal: number;
}

export interface Sale {
  id: string;
  items: SaleItem[];
  total: number;
  date: Date;
  invoiceNumber: string;
}

export interface StockAlert {
  product: Product;
  currentQuantity: number;
  minimumThreshold: number;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  address?: string;
  email?: string;
  createdAt: Date;
}

export interface Supplier {
  id: string;
  name: string;
  phone: string;
  address?: string;
  email?: string;
  createdAt: Date;
}

export interface Debt {
  id: string;
  type: 'customer' | 'supplier'; // مديونية عميل أو مستحق للمورد
  customerId?: string;
  supplierId?: string;
  customerName?: string;
  supplierName?: string;
  amount: number;
  description: string;
  dueDate?: Date;
  status: 'pending' | 'partial' | 'paid';
  createdAt: Date;
  updatedAt: Date;
}

export interface DebtPayment {
  id: string;
  debtId: string;
  amount: number;
  paymentDate: Date;
  paymentMethod: 'cash' | 'bank' | 'check';
  notes?: string;
  createdAt: Date;
}
