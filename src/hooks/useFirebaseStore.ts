import { useState, useEffect } from 'react';
import { 
  productsService, 
  salesService, 
  customersService, 
  suppliersService, 
  debtsService, 
  debtPaymentsService 
} from '@/services/firebaseService';
import { Product, Sale, Customer, Supplier, Debt, DebtPayment } from '@/types';

export const useFirebaseStore = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [debts, setDebts] = useState<Debt[]>([]);
  const [debtPayments, setDebtPayments] = useState<DebtPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize Firebase listeners
  useEffect(() => {
    const unsubscribes: (() => void)[] = [];

    try {
      // Products listener
      const unsubProducts = productsService.onSnapshot((data) => {
        setProducts(data);
      });
      unsubscribes.push(unsubProducts);

      // Sales listener
      const unsubSales = salesService.onSnapshot((data) => {
        setSales(data);
      });
      unsubscribes.push(unsubSales);

      // Customers listener
      const unsubCustomers = customersService.onSnapshot((data) => {
        setCustomers(data);
      });
      unsubscribes.push(unsubCustomers);

      // Suppliers listener
      const unsubSuppliers = suppliersService.onSnapshot((data) => {
        setSuppliers(data);
      });
      unsubscribes.push(unsubSuppliers);

      // Debts listener
      const unsubDebts = debtsService.onSnapshot((data) => {
        setDebts(data);
      });
      unsubscribes.push(unsubDebts);

      // Debt Payments listener
      const unsubDebtPayments = debtPaymentsService.onSnapshot((data) => {
        setDebtPayments(data);
      });
      unsubscribes.push(unsubDebtPayments);

      setLoading(false);
    } catch (err) {
      setError('فشل في الاتصال بقاعدة البيانات');
      setLoading(false);
    }

    // Cleanup function
    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, []);

  // Products operations
  const addProduct = async (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await productsService.add({
        ...product,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (err) {
      setError('فشل في إضافة المنتج');
      throw err;
    }
  };

  const updateProduct = async (id: string, updates: Partial<Product>) => {
    try {
      await productsService.update(id, updates);
    } catch (err) {
      setError('فشل في تحديث المنتج');
      throw err;
    }
  };

  const deleteProduct = async (id: string) => {
    try {
      await productsService.delete(id);
    } catch (err) {
      setError('فشل في حذف المنتج');
      throw err;
    }
  };

  // Sales operations
  const addSale = async (sale: Omit<Sale, 'id'>) => {
    try {
      await salesService.add(sale);
    } catch (err) {
      setError('فشل في إضافة البيع');
      throw err;
    }
  };

  // Customers operations
  const addCustomer = async (customer: Omit<Customer, 'id' | 'createdAt'>) => {
    try {
      await customersService.add({
        ...customer,
        createdAt: new Date()
      });
    } catch (err) {
      setError('فشل في إضافة العميل');
      throw err;
    }
  };

  const updateCustomer = async (id: string, updates: Partial<Customer>) => {
    try {
      await customersService.update(id, updates);
    } catch (err) {
      setError('فشل في تحديث العميل');
      throw err;
    }
  };

  const deleteCustomer = async (id: string) => {
    try {
      await customersService.delete(id);
    } catch (err) {
      setError('فشل في حذف العميل');
      throw err;
    }
  };

  // Suppliers operations
  const addSupplier = async (supplier: Omit<Supplier, 'id' | 'createdAt'>) => {
    try {
      await suppliersService.add({
        ...supplier,
        createdAt: new Date()
      });
    } catch (err) {
      setError('فشل في إضافة المورد');
      throw err;
    }
  };

  const updateSupplier = async (id: string, updates: Partial<Supplier>) => {
    try {
      await suppliersService.update(id, updates);
    } catch (err) {
      setError('فشل في تحديث المورد');
      throw err;
    }
  };

  const deleteSupplier = async (id: string) => {
    try {
      await suppliersService.delete(id);
    } catch (err) {
      setError('فشل في حذف المورد');
      throw err;
    }
  };

  // Debts operations
  const addDebt = async (debt: Omit<Debt, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await debtsService.add({
        ...debt,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (err) {
      setError('فشل في إضافة المديونية');
      throw err;
    }
  };

  const updateDebt = async (id: string, updates: Partial<Debt>) => {
    try {
      await debtsService.update(id, updates);
    } catch (err) {
      setError('فشل في تحديث المديونية');
      throw err;
    }
  };

  const deleteDebt = async (id: string) => {
    try {
      await debtsService.delete(id);
    } catch (err) {
      setError('فشل في حذف المديونية');
      throw err;
    }
  };

  // Debt Payments operations
  const addDebtPayment = async (payment: Omit<DebtPayment, 'id' | 'createdAt'>) => {
    try {
      await debtPaymentsService.add({
        ...payment,
        createdAt: new Date()
      });
      
      // Update debt status
      const debt = debts.find(d => d.id === payment.debtId);
      if (debt) {
        const debtPaymentsList = debtPayments.filter(p => p.debtId === payment.debtId);
        const totalPaid = debtPaymentsList.reduce((sum, p) => sum + p.amount, 0) + payment.amount;
        
        const newStatus = totalPaid >= debt.amount ? 'paid' : 
                        totalPaid > 0 ? 'partial' : 'pending';
        
        await updateDebt(debt.id, { status: newStatus });
      }
    } catch (err) {
      setError('فشل في إضافة الدفعة');
      throw err;
    }
  };

  // Helper functions
  const getStockAlerts = () => {
    return products.filter(product => product.quantity <= 5);
  };

  const getDebtPayments = (debtId: string) => {
    return debtPayments.filter(payment => payment.debtId === debtId);
  };

  const getRemainingDebtAmount = (debtId: string) => {
    const debt = debts.find(d => d.id === debtId);
    if (!debt) return 0;
    
    const totalPaid = debtPayments
      .filter(payment => payment.debtId === debtId)
      .reduce((sum, payment) => sum + payment.amount, 0);
    
    return debt.amount - totalPaid;
  };

  const getProductByCode = (code: string) => {
    return products.find(product => product.code === code);
  };

  return {
    // Data
    products,
    sales,
    customers,
    suppliers,
    debts,
    debtPayments,
    loading,
    error,
    
    // Products operations
    addProduct,
    updateProduct,
    deleteProduct,
    getProductByCode,
    
    // Sales operations
    addSale,
    
    // Customers operations
    addCustomer,
    updateCustomer,
    deleteCustomer,
    
    // Suppliers operations
    addSupplier,
    updateSupplier,
    deleteSupplier,
    
    // Debts operations
    addDebt,
    updateDebt,
    deleteDebt,
    
    // Debt Payments operations
    addDebtPayment,
    getDebtPayments,
    getRemainingDebtAmount,
    
    // Helper functions
    getStockAlerts,
    
    // Clear error
    clearError: () => setError(null)
  };
};
