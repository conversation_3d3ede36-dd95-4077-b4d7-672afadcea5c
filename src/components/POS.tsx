
import { useState, useEffect } from 'react';
import { useStore } from '@/store/useStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Minus, Plus, Trash, Barcode } from 'lucide-react';

export const POS = () => {
  const { 
    products, 
    currentSale, 
    addToCurrentSale, 
    removeFromCurrentSale, 
    updateSaleItemQuantity,
    completeSale,
    clearCurrentSale,
    getProductByCode 
  } = useStore();
  
  const { toast } = useToast();
  const [barcodeInput, setBarcodeInput] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  const total = currentSale.reduce((sum, item) => sum + item.subtotal, 0);

  // Handle barcode scanner input
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Handle Enter key for barcode completion
      if (e.key === 'Enter' && barcodeInput) {
        handleBarcodeSubmit();
      }
    };

    document.addEventListener('keypress', handleKeyPress);
    return () => document.removeEventListener('keypress', handleKeyPress);
  }, [barcodeInput]);

  const handleBarcodeSubmit = () => {
    if (!barcodeInput.trim()) return;

    const product = getProductByCode(barcodeInput.trim());
    if (product) {
      if (product.quantity > 0) {
        addToCurrentSale(product, 1);
        toast({
          title: "تم الإضافة",
          description: `تم إضافة ${product.name} إلى الفاتورة`
        });
      } else {
        toast({
          title: "غير متوفر",
          description: "هذا المنتج غير متوفر في المخزون",
          variant: "destructive"
        });
      }
    } else {
      toast({
        title: "منتج غير موجود",
        description: "لم يتم العثور على منتج بهذا الكود",
        variant: "destructive"
      });
    }
    setBarcodeInput('');
  };

  const handleAddProduct = (product: any) => {
    if (product.quantity > 0) {
      addToCurrentSale(product, 1);
      toast({
        title: "تم الإضافة",
        description: `تم إضافة ${product.name} إلى الفاتورة`
      });
    } else {
      toast({
        title: "غير متوفر",
        description: "هذا المنتج غير متوفر في المخزون",
        variant: "destructive"
      });
    }
  };

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCurrentSale(productId);
    } else {
      updateSaleItemQuantity(productId, newQuantity);
    }
  };

  const handleCompleteSale = () => {
    if (currentSale.length === 0) {
      toast({
        title: "فاتورة فارغة",
        description: "يرجى إضافة منتجات إلى الفاتورة أولاً",
        variant: "destructive"
      });
      return;
    }

    completeSale();
    toast({
      title: "تمت العملية",
      description: `تم إتمام البيع بقيمة ${total.toFixed(2)} ج.م`
    });
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 animate-fade-in">
      {/* Left Side - Products */}
      <div className="lg:col-span-2 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">نقطة البيع</h1>
        </div>

        {/* Barcode Scanner */}
        <Card className="card-hover">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Barcode className="w-5 h-5" />
              قارئ الباركود
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Input
                placeholder="امسح الباركود أو اكتب الكود"
                value={barcodeInput}
                onChange={(e) => setBarcodeInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleBarcodeSubmit()}
                className="flex-1"
                autoFocus
              />
              <Button onClick={handleBarcodeSubmit}>
                إضافة
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Product Search */}
        <Card className="card-hover">
          <CardContent className="pt-6">
            <Input
              placeholder="البحث في المنتجات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </CardContent>
        </Card>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredProducts.map((product) => (
            <Card 
              key={product.id} 
              className={`card-hover cursor-pointer transition-all ${product.quantity <= 0 ? 'opacity-50' : ''}`}
              onClick={() => handleAddProduct(product)}
            >
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{product.name}</h3>
                    <p className="text-sm text-gray-600">كود: {product.code}</p>
                    <p className="text-lg font-bold text-green-600 mt-1">
                      {product.price.toFixed(2)} ج.م
                    </p>
                  </div>
                  <div className="text-left">
                    <div className={`text-sm ${product.quantity <= 5 ? 'text-red-600' : 'text-gray-600'}`}>
                      متوفر: {product.quantity}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Right Side - Current Sale */}
      <div className="space-y-6">
        <Card className="card-hover">
          <CardHeader>
            <CardTitle>الفاتورة الحالية</CardTitle>
          </CardHeader>
          <CardContent>
            {currentSale.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                لا توجد منتجات في الفاتورة
              </div>
            ) : (
              <div className="space-y-4">
                {currentSale.map((item) => (
                  <div key={item.product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {item.product.name}
                      </div>
                      <div className="text-sm text-gray-600">
                        {item.product.price.toFixed(2)} ج.م × {item.quantity}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuantityChange(item.product.id, item.quantity - 1)}
                      >
                        <Minus className="w-4 h-4" />
                      </Button>
                      
                      <span className="w-8 text-center font-medium">
                        {item.quantity}
                      </span>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuantityChange(item.product.id, item.quantity + 1)}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => removeFromCurrentSale(item.product.id)}
                      >
                        <Trash className="w-4 h-4" />
                      </Button>
                    </div>
                    
                    <div className="text-lg font-bold text-green-600 mr-2">
                      {item.subtotal.toFixed(2)} ج.م
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Total and Actions */}
        <Card className="card-hover">
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center text-xl font-bold">
                <span>الإجمالي:</span>
                <span className="text-green-600">{total.toFixed(2)} ج.م</span>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <Button 
                  variant="outline" 
                  onClick={clearCurrentSale}
                  disabled={currentSale.length === 0}
                >
                  مسح الكل
                </Button>
                <Button 
                  onClick={handleCompleteSale}
                  disabled={currentSale.length === 0}
                  className="bg-green-600 hover:bg-green-700"
                >
                  إتمام البيع
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
