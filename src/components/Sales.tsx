
import { useState } from 'react';
import { useStore } from '@/store/useStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export const Sales = () => {
  const { sales } = useStore();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredSales = sales.filter(sale =>
    sale.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePrintInvoice = (sale: any) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <title>فاتورة ${sale.invoiceNumber}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
            .header { text-align: center; margin-bottom: 30px; }
            .invoice-info { margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f2f2f2; }
            .total { font-size: 18px; font-weight: bold; text-align: left; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>🧽 محل المنظفات</h1>
            <h2>فاتورة بيع</h2>
          </div>
          
          <div class="invoice-info">
            <p><strong>رقم الفاتورة:</strong> ${sale.invoiceNumber}</p>
            <p><strong>التاريخ:</strong> ${new Date(sale.date).toLocaleDateString('ar-EG')}</p>
            <p><strong>الوقت:</strong> ${new Date(sale.date).toLocaleTimeString('ar-EG')}</p>
          </div>

          <table>
            <thead>
              <tr>
                <th>المنتج</th>
                <th>الكود</th>
                <th>السعر</th>
                <th>الكمية</th>
                <th>الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              ${sale.items.map((item: any) => `
                <tr>
                  <td>${item.product.name}</td>
                  <td>${item.product.code}</td>
                  <td>${item.product.price.toFixed(2)} ج.م</td>
                  <td>${item.quantity}</td>
                  <td>${item.subtotal.toFixed(2)} ج.م</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="total">
            <p>الإجمالي النهائي: ${sale.total.toFixed(2)} ج.م</p>
          </div>

          <div style="margin-top: 40px; text-align: center; font-size: 14px; color: #666;">
            شكراً لتعاملكم معنا
          </div>
        </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">المبيعات</h1>
      </div>

      {/* Search */}
      <Card className="card-hover">
        <CardContent className="pt-6">
          <Input
            placeholder="البحث برقم الفاتورة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      {/* Sales List */}
      <div className="space-y-4">
        {filteredSales.length === 0 ? (
          <Card className="card-hover">
            <CardContent className="py-12 text-center">
              <div className="text-gray-500 text-lg">
                {searchTerm ? 'لا توجد فواتير مطابقة للبحث' : 'لا توجد مبيعات بعد'}
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredSales.map((sale) => (
            <Card key={sale.id} className="card-hover">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    فاتورة {sale.invoiceNumber}
                  </CardTitle>
                  <div className="text-sm text-gray-600">
                    {new Date(sale.date).toLocaleDateString('ar-EG')} - {new Date(sale.date).toLocaleTimeString('ar-EG')}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sale.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <div className="flex-1">
                        <div className="font-medium">{item.product.name}</div>
                        <div className="text-sm text-gray-600">
                          {item.product.price.toFixed(2)} ج.م × {item.quantity}
                        </div>
                      </div>
                      <div className="font-bold text-green-600">
                        {item.subtotal.toFixed(2)} ج.م
                      </div>
                    </div>
                  ))}
                  
                  <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePrintInvoice(sale)}
                      >
                        طباعة الفاتورة
                      </Button>
                    </div>
                    <div className="text-xl font-bold text-green-600">
                      الإجمالي: {sale.total.toFixed(2)} ج.م
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
