
import { useState } from 'react';
import { useStore } from '@/store/useStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

export const Inventory = () => {
  const { products, updateStock, getStockAlerts } = useStore();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [updateQuantities, setUpdateQuantities] = useState<Record<string, string>>({});

  const stockAlerts = getStockAlerts();
  
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleQuantityUpdate = (productId: string, newQuantity: string) => {
    setUpdateQuantities(prev => ({
      ...prev,
      [productId]: newQuantity
    }));
  };

  const handleSaveQuantity = (productId: string) => {
    const newQuantity = parseInt(updateQuantities[productId] || '0');
    if (newQuantity >= 0) {
      updateStock(productId, newQuantity);
      toast({
        title: "تم التحديث",
        description: "تم تحديث كمية المنتج بنجاح"
      });
      setUpdateQuantities(prev => {
        const newState = { ...prev };
        delete newState[productId];
        return newState;
      });
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">إدارة المخزون</h1>
      </div>

      {/* Stock Alerts */}
      {stockAlerts.length > 0 && (
        <Card className="card-hover border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">
              ⚠️ تنبيهات المخزون ({stockAlerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {stockAlerts.map((product) => (
                <div key={product.id} className="p-3 bg-white rounded-lg border border-red-200">
                  <div className="font-medium text-gray-900">{product.name}</div>
                  <div className="text-sm text-gray-600">كود: {product.code}</div>
                  <div className="text-lg font-bold text-red-600 mt-1">
                    متبقي: {product.quantity}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search */}
      <Card className="card-hover">
        <CardContent className="pt-6">
          <Input
            placeholder="البحث في المنتجات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card className="card-hover">
        <CardHeader>
          <CardTitle>قائمة المنتجات والمخزون</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredProducts.map((product) => (
              <div key={product.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{product.name}</div>
                  <div className="text-sm text-gray-600">
                    كود: {product.code} | السعر: {product.price.toFixed(2)} ج.م
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-sm text-gray-600">الكمية الحالية</div>
                    <div className={`text-lg font-bold ${product.quantity <= 5 ? 'text-red-600' : 'text-green-600'}`}>
                      {product.quantity}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      placeholder="كمية جديدة"
                      value={updateQuantities[product.id] || ''}
                      onChange={(e) => handleQuantityUpdate(product.id, e.target.value)}
                      className="w-24"
                      min="0"
                    />
                    <Button
                      onClick={() => handleSaveQuantity(product.id)}
                      disabled={!updateQuantities[product.id]}
                      size="sm"
                    >
                      تحديث
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm ? 'لا توجد منتجات مطابقة للبحث' : 'لا توجد منتجات في المخزون'}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stock Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="card-hover">
          <CardContent className="pt-6 text-center">
            <div className="text-2xl font-bold text-blue-600">{products.length}</div>
            <div className="text-sm text-gray-600">إجمالي المنتجات</div>
          </CardContent>
        </Card>
        
        <Card className="card-hover">
          <CardContent className="pt-6 text-center">
            <div className="text-2xl font-bold text-green-600">
              {products.reduce((sum, product) => sum + product.quantity, 0)}
            </div>
            <div className="text-sm text-gray-600">إجمالي القطع</div>
          </CardContent>
        </Card>
        
        <Card className="card-hover">
          <CardContent className="pt-6 text-center">
            <div className="text-2xl font-bold text-red-600">{stockAlerts.length}</div>
            <div className="text-sm text-gray-600">منتجات تحتاج تجديد</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
