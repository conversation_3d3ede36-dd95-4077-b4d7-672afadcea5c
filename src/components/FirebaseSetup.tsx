import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Copy, Check, ExternalLink } from 'lucide-react';

export const FirebaseSetup = () => {
  const [copied, setCopied] = useState(false);

  const firebaseConfigTemplate = `// src/lib/firebase.ts
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_PROJECT_ID.appspot.com",
  messagingSenderId: "YOUR_SENDER_ID",
  appId: "YOUR_APP_ID"
};

const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const auth = getAuth(app);
export default app;`;

  const copyToClipboard = () => {
    navigator.clipboard.writeText(firebaseConfigTemplate);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            🔥 إعداد Firebase لنظام إدارة المحل
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <AlertDescription>
              لربط التطبيق بقاعدة بيانات Firebase، يرجى اتباع الخطوات التالية:
            </AlertDescription>
          </Alert>

          {/* Step 1 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الخطوة 1: إنشاء مشروع Firebase</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>اذهب إلى <a href="https://console.firebase.google.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline inline-flex items-center gap-1">Firebase Console <ExternalLink className="w-3 h-3" /></a></li>
              <li>انقر على "إضافة مشروع" أو "Add project"</li>
              <li>أدخل اسم المشروع (مثل: "masrafy-shop-manager")</li>
              <li>اختر إعدادات المشروع حسب تفضيلك</li>
              <li>انقر على "إنشاء المشروع"</li>
            </ol>
          </div>

          {/* Step 2 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الخطوة 2: إضافة تطبيق ويب</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>في لوحة تحكم المشروع، انقر على أيقونة الويب {"</>"}</li>
              <li>أدخل اسم التطبيق (مثل: "Masrafy Shop Manager")</li>
              <li>فعل "Firebase Hosting" إذا كنت تريد استضافة التطبيق</li>
              <li>انقر على "تسجيل التطبيق"</li>
            </ol>
          </div>

          {/* Step 3 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الخطوة 3: إعداد Firestore Database</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>في القائمة الجانبية، انقر على "Firestore Database"</li>
              <li>انقر على "إنشاء قاعدة بيانات"</li>
              <li>اختر "Start in test mode" للبداية</li>
              <li>اختر موقع قاعدة البيانات (يفضل أقرب منطقة جغرافية)</li>
            </ol>
          </div>

          {/* Step 4 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الخطوة 4: إعداد Authentication</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>في القائمة الجانبية، انقر على "Authentication"</li>
              <li>انقر على "البدء"</li>
              <li>في تبويب "Sign-in method"، فعل "Email/Password"</li>
              <li>احفظ الإعدادات</li>
            </ol>
          </div>

          {/* Step 5 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الخطوة 5: نسخ إعدادات التكوين</h3>
            <p className="text-sm text-gray-600">
              في صفحة إعدادات المشروع، ستجد كود التكوين. انسخه واستبدل القيم في الملف التالي:
            </p>
            
            <div className="relative">
              <Textarea
                value={firebaseConfigTemplate}
                readOnly
                className="font-mono text-sm h-64"
              />
              <Button
                onClick={copyToClipboard}
                size="sm"
                className="absolute top-2 left-2"
                variant="outline"
              >
                {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                {copied ? 'تم النسخ' : 'نسخ'}
              </Button>
            </div>
          </div>

          {/* Step 6 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الخطوة 6: إعداد قواعد الأمان</h3>
            <p className="text-sm text-gray-600">
              في Firestore Database، اذهب إلى تبويب "Rules" وأضف القواعد التالية:
            </p>
            
            <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
              <pre>{`rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to all documents
    match /{document=**} {
      allow read, write: if true;
    }
  }
}`}</pre>
            </div>
            
            <Alert>
              <AlertDescription>
                ⚠️ هذه القواعد للتطوير فقط. في الإنتاج، يجب إضافة قواعد أمان أكثر تقييداً.
              </AlertDescription>
            </Alert>
          </div>

          {/* Step 7 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الخطوة 7: تفعيل Firebase في التطبيق</h3>
            <p className="text-sm text-gray-600">
              بعد إعداد التكوين، سيتم تفعيل Firebase تلقائياً في التطبيق وستتمكن من:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>حفظ البيانات في السحابة</li>
              <li>مزامنة البيانات في الوقت الفعلي</li>
              <li>الوصول للبيانات من أي جهاز</li>
              <li>النسخ الاحتياطي التلقائي</li>
            </ul>
          </div>

          <Alert>
            <AlertDescription>
              💡 نصيحة: احتفظ بنسخة من إعدادات Firebase في مكان آمن لاستخدامها لاحقاً.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};
