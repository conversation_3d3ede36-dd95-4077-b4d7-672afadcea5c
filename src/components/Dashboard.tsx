
import { useStore } from '@/store/useStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const Dashboard = () => {
  const { products, sales, getStockAlerts } = useStore();
  
  const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
  const todaysSales = sales.filter(
    sale => new Date(sale.date).toDateString() === new Date().toDateString()
  );
  const todaysRevenue = todaysSales.reduce((sum, sale) => sum + sale.total, 0);
  const stockAlerts = getStockAlerts();

  const topProducts = sales
    .flatMap(sale => sale.items)
    .reduce((acc, item) => {
      const productId = item.product.id;
      acc[productId] = (acc[productId] || 0) + item.quantity;
      return acc;
    }, {} as Record<string, number>);

  const topProductsList = Object.entries(topProducts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([productId, quantity]) => {
      const product = products.find(p => p.id === productId);
      return { product, quantity };
    })
    .filter(item => item.product);

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
        <div className="text-sm text-gray-500">
          {new Date().toLocaleDateString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="card-hover gradient-bg text-white">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium opacity-90">
              إجمالي المبيعات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRevenue.toFixed(2)} ج.م</div>
            <p className="text-sm opacity-80">من {sales.length} عملية بيع</p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900">
              مبيعات اليوم
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {todaysRevenue.toFixed(2)} ج.م
            </div>
            <p className="text-sm text-gray-600">من {todaysSales.length} عملية</p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900">
              عدد المنتجات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{products.length}</div>
            <p className="text-sm text-gray-600">منتج في المخزن</p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900">
              تنبيهات المخزون
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stockAlerts.length}</div>
            <p className="text-sm text-gray-600">منتج ينتهي قريباً</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <Card className="card-hover">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900">
              أكثر المنتجات مبيعاً
            </CardTitle>
          </CardHeader>
          <CardContent>
            {topProductsList.length > 0 ? (
              <div className="space-y-3">
                {topProductsList.map((item, index) => (
                  <div key={item.product?.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {item.product?.name}
                        </div>
                        <div className="text-sm text-gray-600">
                          {item.product?.price} ج.م
                        </div>
                      </div>
                    </div>
                    <div className="text-lg font-bold text-green-600">
                      {item.quantity}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                لا توجد مبيعات بعد
              </div>
            )}
          </CardContent>
        </Card>

        {/* Stock Alerts */}
        <Card className="card-hover">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900">
              تنبيهات المخزون
            </CardTitle>
          </CardHeader>
          <CardContent>
            {stockAlerts.length > 0 ? (
              <div className="space-y-3">
                {stockAlerts.map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                    <div>
                      <div className="font-medium text-gray-900">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-600">
                        كود: {product.code}
                      </div>
                    </div>
                    <div className="text-lg font-bold text-red-600">
                      {product.quantity} متبقي
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                جميع المنتجات متوفرة في المخزون
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
