
import { useState } from 'react';
import { useStore } from '@/store/useStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CalendarDays, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';

export const Dashboard = () => {
  const { products, sales, getStockAlerts } = useStore();
  const [timeFilter, setTimeFilter] = useState('today');

  // Helper functions for date filtering
  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isThisWeek = (date: Date) => {
    const today = new Date();
    const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
    const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));
    return date >= weekStart && date <= weekEnd;
  };

  const isThisMonth = (date: Date) => {
    const today = new Date();
    return date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear();
  };

  // Filter sales based on selected time period
  const getFilteredSales = () => {
    switch (timeFilter) {
      case 'today':
        return sales.filter(sale => isToday(new Date(sale.date)));
      case 'week':
        return sales.filter(sale => isThisWeek(new Date(sale.date)));
      case 'month':
        return sales.filter(sale => isThisMonth(new Date(sale.date)));
      default:
        return sales;
    }
  };

  const filteredSales = getFilteredSales();
  const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
  const filteredRevenue = filteredSales.reduce((sum, sale) => sum + sale.total, 0);

  // Calculate costs and profits
  const calculateTotalCost = (sales: any[]) => {
    return sales.reduce((total, sale) => {
      return total + sale.items.reduce((saleTotal: number, item: any) => {
        return saleTotal + (item.product.costPrice || 0) * item.quantity;
      }, 0);
    }, 0);
  };

  const filteredCost = calculateTotalCost(filteredSales);
  const filteredProfit = filteredRevenue - filteredCost;

  const stockAlerts = getStockAlerts();

  const topProducts = sales
    .flatMap(sale => sale.items)
    .reduce((acc, item) => {
      const productId = item.product.id;
      acc[productId] = (acc[productId] || 0) + item.quantity;
      return acc;
    }, {} as Record<string, number>);

  const topProductsList = Object.entries(topProducts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([productId, quantity]) => {
      const product = products.find(p => p.id === productId);
      return { product, quantity };
    })
    .filter(item => item.product);

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">لوحة التحكم</h1>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <CalendarDays className="w-4 h-4 text-gray-500" />
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="week">هذا الأسبوع</SelectItem>
                <SelectItem value="month">هذا الشهر</SelectItem>
                <SelectItem value="all">الكل</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {new Date().toLocaleDateString('ar-EG', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card className="card-hover gradient-bg text-white">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium opacity-90 flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              المبيعات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredRevenue.toFixed(2)} ج.م</div>
            <p className="text-sm opacity-80">من {filteredSales.length} عملية</p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
              <TrendingDown className="w-5 h-5 text-red-500" />
              التكلفة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {filteredCost.toFixed(2)} ج.م
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">تكلفة المبيعات</p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-500" />
              الربح
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {filteredProfit.toFixed(2)} ج.م
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              هامش ربح: {filteredRevenue > 0 ? ((filteredProfit / filteredRevenue) * 100).toFixed(1) : 0}%
            </p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900 dark:text-white">
              عدد المنتجات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{products.length}</div>
            <p className="text-sm text-gray-600 dark:text-gray-400">منتج في المخزن</p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900 dark:text-white">
              تنبيهات المخزون
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stockAlerts.length}</div>
            <p className="text-sm text-gray-600 dark:text-gray-400">منتج ينتهي قريباً</p>
          </CardContent>
        </Card>
      </div>

      {/* Time Period Summary */}
      <Card className="card-hover">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
            ملخص الفترة المحددة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">
                {filteredSales.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">عدد المبيعات</div>
            </div>
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-2">
                {filteredRevenue.toFixed(2)} ج.م
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">إجمالي المبيعات</div>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-2">
                {filteredSales.length > 0 ? (filteredRevenue / filteredSales.length).toFixed(2) : '0.00'} ج.م
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">متوسط قيمة البيع</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <Card className="card-hover">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
              أكثر المنتجات مبيعاً
            </CardTitle>
          </CardHeader>
          <CardContent>
            {topProductsList.length > 0 ? (
              <div className="space-y-3">
                {topProductsList.map((item, index) => (
                  <div key={item.product?.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {item.product?.name}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {item.product?.price} ج.م - ربح: {((item.product?.price || 0) - (item.product?.costPrice || 0)).toFixed(2)} ج.م
                        </div>
                      </div>
                    </div>
                    <div className="text-lg font-bold text-green-600">
                      {item.quantity}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                لا توجد مبيعات بعد
              </div>
            )}
          </CardContent>
        </Card>

        {/* Stock Alerts */}
        <Card className="card-hover">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
              تنبيهات المخزون
            </CardTitle>
          </CardHeader>
          <CardContent>
            {stockAlerts.length > 0 ? (
              <div className="space-y-3">
                {stockAlerts.map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        كود: {product.code} - قيمة المخزون: {((product.costPrice || 0) * product.quantity).toFixed(2)} ج.م
                      </div>
                    </div>
                    <div className="text-lg font-bold text-red-600">
                      {product.quantity} متبقي
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                جميع المنتجات متوفرة في المخزون
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
