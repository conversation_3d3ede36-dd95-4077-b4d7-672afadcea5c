import React, { createContext, useContext, useState, useEffect } from 'react';
import { useStore } from '@/store/useStore';
import { useFirebaseStore } from '@/hooks/useFirebaseStore';

interface DataContextType {
  isFirebaseEnabled: boolean;
  enableFirebase: () => void;
  disableFirebase: () => void;
  migrateToFirebase: () => Promise<void>;
  loading: boolean;
  error: string | null;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isFirebaseEnabled, setIsFirebaseEnabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const localStore = useStore();
  const firebaseStore = useFirebaseStore();

  // Check if Firebase is configured
  useEffect(() => {
    const firebaseConfig = localStorage.getItem('firebaseEnabled');
    if (firebaseConfig === 'true') {
      setIsFirebaseEnabled(true);
    }
  }, []);

  const enableFirebase = () => {
    setIsFirebaseEnabled(true);
    localStorage.setItem('firebaseEnabled', 'true');
  };

  const disableFirebase = () => {
    setIsFirebaseEnabled(false);
    localStorage.setItem('firebaseEnabled', 'false');
  };

  const migrateToFirebase = async () => {
    setLoading(true);
    setError(null);

    try {
      // Migrate products
      for (const product of localStore.products) {
        await firebaseStore.addProduct({
          name: product.name,
          price: product.price,
          costPrice: product.costPrice || 0,
          quantity: product.quantity,
          code: product.code,
          description: product.description
        });
      }

      // Migrate customers
      for (const customer of localStore.customers) {
        await firebaseStore.addCustomer({
          name: customer.name,
          phone: customer.phone,
          address: customer.address,
          email: customer.email
        });
      }

      // Migrate suppliers
      for (const supplier of localStore.suppliers) {
        await firebaseStore.addSupplier({
          name: supplier.name,
          phone: supplier.phone,
          address: supplier.address,
          email: supplier.email
        });
      }

      // Migrate sales
      for (const sale of localStore.sales) {
        await firebaseStore.addSale(sale);
      }

      // Migrate debts
      for (const debt of localStore.debts) {
        await firebaseStore.addDebt({
          type: debt.type,
          customerId: debt.customerId,
          supplierId: debt.supplierId,
          customerName: debt.customerName,
          supplierName: debt.supplierName,
          amount: debt.amount,
          description: debt.description,
          dueDate: debt.dueDate,
          status: debt.status
        });
      }

      // Migrate debt payments
      for (const payment of localStore.debtPayments) {
        await firebaseStore.addDebtPayment({
          debtId: payment.debtId,
          amount: payment.amount,
          paymentDate: payment.paymentDate,
          paymentMethod: payment.paymentMethod,
          notes: payment.notes
        });
      }

      enableFirebase();
    } catch (err) {
      setError('فشل في نقل البيانات إلى Firebase');
      console.error('Migration error:', err);
    } finally {
      setLoading(false);
    }
  };

  const value = {
    isFirebaseEnabled,
    enableFirebase,
    disableFirebase,
    migrateToFirebase,
    loading,
    error
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

export const useDataContext = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useDataContext must be used within a DataProvider');
  }
  return context;
};
