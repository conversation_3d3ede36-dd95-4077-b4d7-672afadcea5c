
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Product, Sale, SaleItem, Customer, Supplier, Debt, DebtPayment } from '../types';

interface Store {
  products: Product[];
  sales: Sale[];
  currentSale: SaleItem[];
  customers: Customer[];
  suppliers: Supplier[];
  debts: Debt[];
  debtPayments: DebtPayment[];
  
  // Products
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (id: string, product: Partial<Product>) => void;
  deleteProduct: (id: string) => void;
  getProductByCode: (code: string) => Product | undefined;
  
  // Sales
  addToCurrentSale: (product: Product, quantity: number) => void;
  removeFromCurrentSale: (productId: string) => void;
  updateSaleItemQuantity: (productId: string, quantity: number) => void;
  completeSale: () => void;
  clearCurrentSale: () => void;
  
  // Stock management
  updateStock: (productId: string, newQuantity: number) => void;
  getStockAlerts: () => Product[];

  // Customers
  addCustomer: (customer: Omit<Customer, 'id' | 'createdAt'>) => void;
  updateCustomer: (id: string, customer: Partial<Customer>) => void;
  deleteCustomer: (id: string) => void;

  // Suppliers
  addSupplier: (supplier: Omit<Supplier, 'id' | 'createdAt'>) => void;
  updateSupplier: (id: string, supplier: Partial<Supplier>) => void;
  deleteSupplier: (id: string) => void;

  // Debts
  addDebt: (debt: Omit<Debt, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateDebt: (id: string, debt: Partial<Debt>) => void;
  deleteDebt: (id: string) => void;

  // Debt Payments
  addDebtPayment: (payment: Omit<DebtPayment, 'id' | 'createdAt'>) => void;
  getDebtPayments: (debtId: string) => DebtPayment[];
  getTotalDebtAmount: (debtId: string) => number;
  getRemainingDebtAmount: (debtId: string) => number;
}

const generateId = () => Math.random().toString(36).substr(2, 9);
const generateInvoiceNumber = () => `INV-${Date.now()}`;

export const useStore = create<Store>()(
  persist(
    (set, get) => ({
      products: [],
      sales: [],
      currentSale: [],
      customers: [],
      suppliers: [],
      debts: [],
      debtPayments: [],

      addProduct: (productData) => {
        const product: Product = {
          ...productData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        set((state) => ({
          products: [...state.products, product],
        }));
      },

      updateProduct: (id, updates) => {
        set((state) => ({
          products: state.products.map((product) =>
            product.id === id
              ? { ...product, ...updates, updatedAt: new Date() }
              : product
          ),
        }));
      },

      deleteProduct: (id) => {
        set((state) => ({
          products: state.products.filter((product) => product.id !== id),
        }));
      },

      getProductByCode: (code) => {
        const { products } = get();
        return products.find((product) => product.code === code);
      },

      addToCurrentSale: (product, quantity) => {
        set((state) => {
          const existingItem = state.currentSale.find(
            (item) => item.product.id === product.id
          );

          if (existingItem) {
            return {
              currentSale: state.currentSale.map((item) =>
                item.product.id === product.id
                  ? {
                      ...item,
                      quantity: item.quantity + quantity,
                      subtotal: (item.quantity + quantity) * product.price,
                    }
                  : item
              ),
            };
          }

          return {
            currentSale: [
              ...state.currentSale,
              {
                product,
                quantity,
                subtotal: quantity * product.price,
              },
            ],
          };
        });
      },

      removeFromCurrentSale: (productId) => {
        set((state) => ({
          currentSale: state.currentSale.filter(
            (item) => item.product.id !== productId
          ),
        }));
      },

      updateSaleItemQuantity: (productId, quantity) => {
        set((state) => ({
          currentSale: state.currentSale.map((item) =>
            item.product.id === productId
              ? {
                  ...item,
                  quantity,
                  subtotal: quantity * item.product.price,
                }
              : item
          ),
        }));
      },

      completeSale: () => {
        const { currentSale } = get();
        if (currentSale.length === 0) return;

        const sale: Sale = {
          id: generateId(),
          items: currentSale,
          total: currentSale.reduce((sum, item) => sum + item.subtotal, 0),
          date: new Date(),
          invoiceNumber: generateInvoiceNumber(),
        };

        // Update product quantities
        currentSale.forEach((item) => {
          const { updateProduct } = get();
          updateProduct(item.product.id, {
            quantity: item.product.quantity - item.quantity,
          });
        });

        set((state) => ({
          sales: [...state.sales, sale],
          currentSale: [],
        }));
      },

      clearCurrentSale: () => {
        set({ currentSale: [] });
      },

      updateStock: (productId, newQuantity) => {
        const { updateProduct } = get();
        updateProduct(productId, { quantity: newQuantity });
      },

      getStockAlerts: () => {
        const { products } = get();
        return products.filter((product) => product.quantity <= 5);
      },

      // Customers
      addCustomer: (customerData) => {
        const customer: Customer = {
          ...customerData,
          id: generateId(),
          createdAt: new Date(),
        };
        set((state) => ({
          customers: [...state.customers, customer],
        }));
      },

      updateCustomer: (id, updates) => {
        set((state) => ({
          customers: state.customers.map((customer) =>
            customer.id === id ? { ...customer, ...updates } : customer
          ),
        }));
      },

      deleteCustomer: (id) => {
        set((state) => ({
          customers: state.customers.filter((customer) => customer.id !== id),
        }));
      },

      // Suppliers
      addSupplier: (supplierData) => {
        const supplier: Supplier = {
          ...supplierData,
          id: generateId(),
          createdAt: new Date(),
        };
        set((state) => ({
          suppliers: [...state.suppliers, supplier],
        }));
      },

      updateSupplier: (id, updates) => {
        set((state) => ({
          suppliers: state.suppliers.map((supplier) =>
            supplier.id === id ? { ...supplier, ...updates } : supplier
          ),
        }));
      },

      deleteSupplier: (id) => {
        set((state) => ({
          suppliers: state.suppliers.filter((supplier) => supplier.id !== id),
        }));
      },

      // Debts
      addDebt: (debtData) => {
        const debt: Debt = {
          ...debtData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        set((state) => ({
          debts: [...state.debts, debt],
        }));
      },

      updateDebt: (id, updates) => {
        set((state) => ({
          debts: state.debts.map((debt) =>
            debt.id === id
              ? { ...debt, ...updates, updatedAt: new Date() }
              : debt
          ),
        }));
      },

      deleteDebt: (id) => {
        set((state) => ({
          debts: state.debts.filter((debt) => debt.id !== id),
        }));
      },

      // Debt Payments
      addDebtPayment: (paymentData) => {
        const payment: DebtPayment = {
          ...paymentData,
          id: generateId(),
          createdAt: new Date(),
        };

        set((state) => {
          const newPayments = [...state.debtPayments, payment];

          // Update debt status based on payments
          const debt = state.debts.find(d => d.id === payment.debtId);
          if (debt) {
            const totalPaid = newPayments
              .filter(p => p.debtId === payment.debtId)
              .reduce((sum, p) => sum + p.amount, 0);

            const newStatus = totalPaid >= debt.amount ? 'paid' :
                            totalPaid > 0 ? 'partial' : 'pending';

            const updatedDebts = state.debts.map(d =>
              d.id === payment.debtId
                ? { ...d, status: newStatus, updatedAt: new Date() }
                : d
            );

            return {
              debtPayments: newPayments,
              debts: updatedDebts,
            };
          }

          return { debtPayments: newPayments };
        });
      },

      getDebtPayments: (debtId) => {
        const { debtPayments } = get();
        return debtPayments.filter((payment) => payment.debtId === debtId);
      },

      getTotalDebtAmount: (debtId) => {
        const { debts } = get();
        const debt = debts.find((d) => d.id === debtId);
        return debt ? debt.amount : 0;
      },

      getRemainingDebtAmount: (debtId) => {
        const { debts, debtPayments } = get();
        const debt = debts.find((d) => d.id === debtId);
        if (!debt) return 0;

        const totalPaid = debtPayments
          .filter((payment) => payment.debtId === debtId)
          .reduce((sum, payment) => sum + payment.amount, 0);

        return debt.amount - totalPaid;
      },
    }),
    {
      name: 'shop-storage',
    }
  )
);
