
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Product, Sale, SaleItem } from '../types';

interface Store {
  products: Product[];
  sales: Sale[];
  currentSale: SaleItem[];
  
  // Products
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (id: string, product: Partial<Product>) => void;
  deleteProduct: (id: string) => void;
  getProductByCode: (code: string) => Product | undefined;
  
  // Sales
  addToCurrentSale: (product: Product, quantity: number) => void;
  removeFromCurrentSale: (productId: string) => void;
  updateSaleItemQuantity: (productId: string, quantity: number) => void;
  completeSale: () => void;
  clearCurrentSale: () => void;
  
  // Stock management
  updateStock: (productId: string, newQuantity: number) => void;
  getStockAlerts: () => Product[];
}

const generateId = () => Math.random().toString(36).substr(2, 9);
const generateInvoiceNumber = () => `INV-${Date.now()}`;

export const useStore = create<Store>()(
  persist(
    (set, get) => ({
      products: [],
      sales: [],
      currentSale: [],

      addProduct: (productData) => {
        const product: Product = {
          ...productData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        set((state) => ({
          products: [...state.products, product],
        }));
      },

      updateProduct: (id, updates) => {
        set((state) => ({
          products: state.products.map((product) =>
            product.id === id
              ? { ...product, ...updates, updatedAt: new Date() }
              : product
          ),
        }));
      },

      deleteProduct: (id) => {
        set((state) => ({
          products: state.products.filter((product) => product.id !== id),
        }));
      },

      getProductByCode: (code) => {
        const { products } = get();
        return products.find((product) => product.code === code);
      },

      addToCurrentSale: (product, quantity) => {
        set((state) => {
          const existingItem = state.currentSale.find(
            (item) => item.product.id === product.id
          );

          if (existingItem) {
            return {
              currentSale: state.currentSale.map((item) =>
                item.product.id === product.id
                  ? {
                      ...item,
                      quantity: item.quantity + quantity,
                      subtotal: (item.quantity + quantity) * product.price,
                    }
                  : item
              ),
            };
          }

          return {
            currentSale: [
              ...state.currentSale,
              {
                product,
                quantity,
                subtotal: quantity * product.price,
              },
            ],
          };
        });
      },

      removeFromCurrentSale: (productId) => {
        set((state) => ({
          currentSale: state.currentSale.filter(
            (item) => item.product.id !== productId
          ),
        }));
      },

      updateSaleItemQuantity: (productId, quantity) => {
        set((state) => ({
          currentSale: state.currentSale.map((item) =>
            item.product.id === productId
              ? {
                  ...item,
                  quantity,
                  subtotal: quantity * item.product.price,
                }
              : item
          ),
        }));
      },

      completeSale: () => {
        const { currentSale } = get();
        if (currentSale.length === 0) return;

        const sale: Sale = {
          id: generateId(),
          items: currentSale,
          total: currentSale.reduce((sum, item) => sum + item.subtotal, 0),
          date: new Date(),
          invoiceNumber: generateInvoiceNumber(),
        };

        // Update product quantities
        currentSale.forEach((item) => {
          const { updateProduct } = get();
          updateProduct(item.product.id, {
            quantity: item.product.quantity - item.quantity,
          });
        });

        set((state) => ({
          sales: [...state.sales, sale],
          currentSale: [],
        }));
      },

      clearCurrentSale: () => {
        set({ currentSale: [] });
      },

      updateStock: (productId, newQuantity) => {
        const { updateProduct } = get();
        updateProduct(productId, { quantity: newQuantity });
      },

      getStockAlerts: () => {
        const { products } = get();
        return products.filter((product) => product.quantity <= 5);
      },
    }),
    {
      name: 'shop-storage',
    }
  )
);
