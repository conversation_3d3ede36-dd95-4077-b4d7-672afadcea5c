import { useState } from 'react';
import { useStore } from '@/store/useStore';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  Store, 
  Printer, 
  DollarSign, 
  Database, 
  Download, 
  Upload,
  Save,
  RotateCcw,
  Shield,
  Bell
} from 'lucide-react';

const Settings = () => {
  const { user } = useAuth();
  const { products, sales } = useStore();
  const { toast } = useToast();
  
  const [storeSettings, setStoreSettings] = useState({
    storeName: 'محل المنظفات الحديث',
    storeAddress: 'شارع الجمهورية، المنصورة، مصر',
    storePhone: '0*********0',
    storeEmail: '<EMAIL>',
    taxNumber: '*********',
    currency: 'ج.م',
    taxRate: 14
  });

  const [printSettings, setPrintSettings] = useState({
    printerName: 'Default Printer',
    paperSize: 'A4',
    printLogo: true,
    printFooter: true,
    footerText: 'شكراً لزيارتكم - نتمنى لكم يوماً سعيداً'
  });

  const [notificationSettings, setNotificationSettings] = useState({
    lowStockAlert: true,
    lowStockThreshold: 5,
    dailyReports: true,
    emailNotifications: false
  });

  const handleSaveStoreSettings = () => {
    // Save store settings to localStorage or backend
    localStorage.setItem('storeSettings', JSON.stringify(storeSettings));
    toast({
      title: "تم الحفظ",
      description: "تم حفظ إعدادات المحل بنجاح"
    });
  };

  const handleSavePrintSettings = () => {
    localStorage.setItem('printSettings', JSON.stringify(printSettings));
    toast({
      title: "تم الحفظ",
      description: "تم حفظ إعدادات الطباعة بنجاح"
    });
  };

  const handleSaveNotificationSettings = () => {
    localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));
    toast({
      title: "تم الحفظ",
      description: "تم حفظ إعدادات التنبيهات بنجاح"
    });
  };

  const handleExportData = () => {
    const data = {
      products,
      sales,
      exportDate: new Date().toISOString(),
      version: '2.0'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "تم التصدير",
      description: "تم تصدير البيانات بنجاح"
    });
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        // Here you would restore the data to your store
        toast({
          title: "تم الاستيراد",
          description: "تم استيراد البيانات بنجاح"
        });
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في استيراد البيانات",
          variant: "destructive"
        });
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">الإعدادات</h1>
      </div>

      <Tabs defaultValue="store" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="store" className="flex items-center gap-2">
            <Store className="w-4 h-4" />
            إعدادات المحل
          </TabsTrigger>
          <TabsTrigger value="print" className="flex items-center gap-2">
            <Printer className="w-4 h-4" />
            الطباعة
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="w-4 h-4" />
            التنبيهات
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            البيانات
          </TabsTrigger>
        </TabsList>

        {/* Store Settings */}
        <TabsContent value="store">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Store className="w-5 h-5" />
                إعدادات المحل
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="storeName">اسم المحل</Label>
                  <Input
                    id="storeName"
                    value={storeSettings.storeName}
                    onChange={(e) => setStoreSettings({...storeSettings, storeName: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="storePhone">رقم الهاتف</Label>
                  <Input
                    id="storePhone"
                    value={storeSettings.storePhone}
                    onChange={(e) => setStoreSettings({...storeSettings, storePhone: e.target.value})}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="storeAddress">عنوان المحل</Label>
                <Textarea
                  id="storeAddress"
                  value={storeSettings.storeAddress}
                  onChange={(e) => setStoreSettings({...storeSettings, storeAddress: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="storeEmail">البريد الإلكتروني</Label>
                  <Input
                    id="storeEmail"
                    type="email"
                    value={storeSettings.storeEmail}
                    onChange={(e) => setStoreSettings({...storeSettings, storeEmail: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="taxNumber">الرقم الضريبي</Label>
                  <Input
                    id="taxNumber"
                    value={storeSettings.taxNumber}
                    onChange={(e) => setStoreSettings({...storeSettings, taxNumber: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="taxRate">معدل الضريبة (%)</Label>
                  <Input
                    id="taxRate"
                    type="number"
                    value={storeSettings.taxRate}
                    onChange={(e) => setStoreSettings({...storeSettings, taxRate: parseFloat(e.target.value)})}
                  />
                </div>
              </div>

              <Separator />
              
              <div className="flex justify-end">
                <Button onClick={handleSaveStoreSettings} className="gap-2">
                  <Save className="w-4 h-4" />
                  حفظ الإعدادات
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Print Settings */}
        <TabsContent value="print">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Printer className="w-5 h-5" />
                إعدادات الطباعة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="printerName">اسم الطابعة</Label>
                  <Input
                    id="printerName"
                    value={printSettings.printerName}
                    onChange={(e) => setPrintSettings({...printSettings, printerName: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="paperSize">حجم الورق</Label>
                  <Input
                    id="paperSize"
                    value={printSettings.paperSize}
                    onChange={(e) => setPrintSettings({...printSettings, paperSize: e.target.value})}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="printLogo">طباعة الشعار</Label>
                  <Switch
                    id="printLogo"
                    checked={printSettings.printLogo}
                    onCheckedChange={(checked) => setPrintSettings({...printSettings, printLogo: checked})}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="printFooter">طباعة التذييل</Label>
                  <Switch
                    id="printFooter"
                    checked={printSettings.printFooter}
                    onCheckedChange={(checked) => setPrintSettings({...printSettings, printFooter: checked})}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="footerText">نص التذييل</Label>
                <Textarea
                  id="footerText"
                  value={printSettings.footerText}
                  onChange={(e) => setPrintSettings({...printSettings, footerText: e.target.value})}
                  rows={2}
                />
              </div>

              <Separator />
              
              <div className="flex justify-end">
                <Button onClick={handleSavePrintSettings} className="gap-2">
                  <Save className="w-4 h-4" />
                  حفظ الإعدادات
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="w-5 h-5" />
                إعدادات التنبيهات
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>تنبيهات نفاد المخزون</Label>
                    <p className="text-sm text-gray-500">تلقي تنبيهات عند انخفاض كمية المنتجات</p>
                  </div>
                  <Switch
                    checked={notificationSettings.lowStockAlert}
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, lowStockAlert: checked})}
                  />
                </div>

                <div>
                  <Label htmlFor="lowStockThreshold">حد التنبيه للمخزون</Label>
                  <Input
                    id="lowStockThreshold"
                    type="number"
                    value={notificationSettings.lowStockThreshold}
                    onChange={(e) => setNotificationSettings({...notificationSettings, lowStockThreshold: parseInt(e.target.value)})}
                    className="max-w-xs"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>التقارير اليومية</Label>
                    <p className="text-sm text-gray-500">إرسال تقرير يومي بالمبيعات</p>
                  </div>
                  <Switch
                    checked={notificationSettings.dailyReports}
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, dailyReports: checked})}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>التنبيهات عبر البريد الإلكتروني</Label>
                    <p className="text-sm text-gray-500">تلقي التنبيهات عبر البريد الإلكتروني</p>
                  </div>
                  <Switch
                    checked={notificationSettings.emailNotifications}
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, emailNotifications: checked})}
                  />
                </div>
              </div>

              <Separator />
              
              <div className="flex justify-end">
                <Button onClick={handleSaveNotificationSettings} className="gap-2">
                  <Save className="w-4 h-4" />
                  حفظ الإعدادات
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Data Management */}
        <TabsContent value="data">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="w-5 h-5" />
                  تصدير البيانات
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">
                  قم بتصدير جميع بيانات المنتجات والمبيعات كنسخة احتياطية
                </p>
                <Button onClick={handleExportData} className="w-full gap-2">
                  <Download className="w-4 h-4" />
                  تصدير البيانات
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  استيراد البيانات
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">
                  استيراد البيانات من نسخة احتياطية سابقة
                </p>
                <div>
                  <Input
                    type="file"
                    accept=".json"
                    onChange={handleImportData}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
