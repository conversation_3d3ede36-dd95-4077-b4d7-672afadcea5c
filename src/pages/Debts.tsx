import { useState } from 'react';
import { useStore } from '@/store/useStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  Users, 
  Truck, 
  CreditCard, 
  DollarSign,
  Calendar,
  Edit,
  Trash,
  Eye,
  AlertCircle
} from 'lucide-react';
import { Debt, Customer, Supplier } from '@/types';

const Debts = () => {
  const { 
    customers, 
    suppliers, 
    debts, 
    debtPayments,
    addCustomer, 
    addSupplier, 
    addDebt, 
    addDebtPayment,
    updateDebt,
    deleteDebt,
    getRemainingDebtAmount 
  } = useStore();
  
  const { toast } = useToast();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [isSupplierDialogOpen, setIsSupplierDialogOpen] = useState(false);
  const [isDebtDialogOpen, setIsDebtDialogOpen] = useState(false);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedDebt, setSelectedDebt] = useState<Debt | null>(null);
  
  const [customerForm, setCustomerForm] = useState({
    name: '',
    phone: '',
    address: '',
    email: ''
  });
  
  const [supplierForm, setSupplierForm] = useState({
    name: '',
    phone: '',
    address: '',
    email: ''
  });
  
  const [debtForm, setDebtForm] = useState({
    type: 'customer' as 'customer' | 'supplier',
    customerId: '',
    supplierId: '',
    amount: '',
    description: '',
    dueDate: ''
  });
  
  const [paymentForm, setPaymentForm] = useState({
    amount: '',
    paymentMethod: 'cash' as 'cash' | 'bank' | 'check',
    notes: ''
  });

  // Statistics
  const customerDebts = debts.filter(debt => debt.type === 'customer');
  const supplierDebts = debts.filter(debt => debt.type === 'supplier');
  
  const totalCustomerDebts = customerDebts.reduce((sum, debt) => sum + getRemainingDebtAmount(debt.id), 0);
  const totalSupplierDebts = supplierDebts.reduce((sum, debt) => sum + getRemainingDebtAmount(debt.id), 0);
  
  const pendingCustomerDebts = customerDebts.filter(debt => debt.status === 'pending').length;
  const pendingSupplierDebts = supplierDebts.filter(debt => debt.status === 'pending').length;

  const handleAddCustomer = () => {
    if (!customerForm.name || !customerForm.phone) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    addCustomer(customerForm);
    setCustomerForm({ name: '', phone: '', address: '', email: '' });
    setIsCustomerDialogOpen(false);
    toast({
      title: "تم الإضافة",
      description: "تم إضافة العميل بنجاح"
    });
  };

  const handleAddSupplier = () => {
    if (!supplierForm.name || !supplierForm.phone) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    addSupplier(supplierForm);
    setSupplierForm({ name: '', phone: '', address: '', email: '' });
    setIsSupplierDialogOpen(false);
    toast({
      title: "تم الإضافة",
      description: "تم إضافة المورد بنجاح"
    });
  };

  const handleAddDebt = () => {
    if (!debtForm.amount || !debtForm.description) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    if (debtForm.type === 'customer' && !debtForm.customerId) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار العميل",
        variant: "destructive"
      });
      return;
    }

    if (debtForm.type === 'supplier' && !debtForm.supplierId) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار المورد",
        variant: "destructive"
      });
      return;
    }

    const customer = customers.find(c => c.id === debtForm.customerId);
    const supplier = suppliers.find(s => s.id === debtForm.supplierId);

    addDebt({
      type: debtForm.type,
      customerId: debtForm.customerId || undefined,
      supplierId: debtForm.supplierId || undefined,
      customerName: customer?.name,
      supplierName: supplier?.name,
      amount: parseFloat(debtForm.amount),
      description: debtForm.description,
      dueDate: debtForm.dueDate ? new Date(debtForm.dueDate) : undefined,
      status: 'pending'
    });

    setDebtForm({
      type: 'customer',
      customerId: '',
      supplierId: '',
      amount: '',
      description: '',
      dueDate: ''
    });
    setIsDebtDialogOpen(false);
    toast({
      title: "تم الإضافة",
      description: "تم إضافة المديونية بنجاح"
    });
  };

  const handleAddPayment = () => {
    if (!selectedDebt || !paymentForm.amount) {
      toast({
        title: "خطأ",
        description: "يرجى ملء المبلغ",
        variant: "destructive"
      });
      return;
    }

    const amount = parseFloat(paymentForm.amount);
    const remaining = getRemainingDebtAmount(selectedDebt.id);

    if (amount > remaining) {
      toast({
        title: "خطأ",
        description: "المبلغ أكبر من المبلغ المتبقي",
        variant: "destructive"
      });
      return;
    }

    addDebtPayment({
      debtId: selectedDebt.id,
      amount,
      paymentDate: new Date(),
      paymentMethod: paymentForm.paymentMethod,
      notes: paymentForm.notes
    });

    setPaymentForm({ amount: '', paymentMethod: 'cash', notes: '' });
    setIsPaymentDialogOpen(false);
    setSelectedDebt(null);
    toast({
      title: "تم الإضافة",
      description: "تم تسجيل الدفعة بنجاح"
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge variant="default" className="bg-green-500">مدفوع</Badge>;
      case 'partial':
        return <Badge variant="secondary">مدفوع جزئياً</Badge>;
      default:
        return <Badge variant="destructive">معلق</Badge>;
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">المديونيات والمستحقات</h1>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
              <CreditCard className="w-5 h-5 text-red-500" />
              مديونيات العملاء
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {totalCustomerDebts.toFixed(2)} ج.م
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {pendingCustomerDebts} مديونية معلقة
            </p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-orange-500" />
              مستحقات الموردين
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {totalSupplierDebts.toFixed(2)} ج.م
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {pendingSupplierDebts} مستحق معلق
            </p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-500" />
              العملاء
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{customers.length}</div>
            <p className="text-sm text-gray-600 dark:text-gray-400">عميل مسجل</p>
          </CardContent>
        </Card>

        <Card className="card-hover">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
              <Truck className="w-5 h-5 text-green-500" />
              الموردين
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{suppliers.length}</div>
            <p className="text-sm text-gray-600 dark:text-gray-400">مورد مسجل</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="customers">العملاء</TabsTrigger>
          <TabsTrigger value="suppliers">الموردين</TabsTrigger>
          <TabsTrigger value="debts">المديونيات</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Customer Debts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="w-5 h-5 text-red-500" />
                  مديونيات العملاء المعلقة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {customerDebts.filter(debt => debt.status !== 'paid').slice(0, 5).map((debt) => (
                  <div key={debt.id} className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg mb-2">
                    <div>
                      <div className="font-medium">{debt.customerName}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{debt.description}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-red-600">
                        {getRemainingDebtAmount(debt.id).toFixed(2)} ج.م
                      </div>
                      {getStatusBadge(debt.status)}
                    </div>
                  </div>
                ))}
                {customerDebts.filter(debt => debt.status !== 'paid').length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد مديونيات معلقة
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Supplier Debts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="w-5 h-5 text-orange-500" />
                  مستحقات الموردين المعلقة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {supplierDebts.filter(debt => debt.status !== 'paid').slice(0, 5).map((debt) => (
                  <div key={debt.id} className="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg mb-2">
                    <div>
                      <div className="font-medium">{debt.supplierName}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{debt.description}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-orange-600">
                        {getRemainingDebtAmount(debt.id).toFixed(2)} ج.م
                      </div>
                      {getStatusBadge(debt.status)}
                    </div>
                  </div>
                ))}
                {supplierDebts.filter(debt => debt.status !== 'paid').length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد مستحقات معلقة
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">إدارة العملاء</h2>
            <Dialog open={isCustomerDialogOpen} onOpenChange={setIsCustomerDialogOpen}>
              <DialogTrigger asChild>
                <Button className="gap-2">
                  <Plus className="w-4 h-4" />
                  إضافة عميل جديد
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>إضافة عميل جديد</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="customerName">اسم العميل *</Label>
                    <Input
                      id="customerName"
                      value={customerForm.name}
                      onChange={(e) => setCustomerForm({...customerForm, name: e.target.value})}
                      placeholder="اسم العميل"
                    />
                  </div>
                  <div>
                    <Label htmlFor="customerPhone">رقم الهاتف *</Label>
                    <Input
                      id="customerPhone"
                      value={customerForm.phone}
                      onChange={(e) => setCustomerForm({...customerForm, phone: e.target.value})}
                      placeholder="رقم الهاتف"
                    />
                  </div>
                  <div>
                    <Label htmlFor="customerAddress">العنوان</Label>
                    <Textarea
                      id="customerAddress"
                      value={customerForm.address}
                      onChange={(e) => setCustomerForm({...customerForm, address: e.target.value})}
                      placeholder="العنوان"
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="customerEmail">البريد الإلكتروني</Label>
                    <Input
                      id="customerEmail"
                      type="email"
                      value={customerForm.email}
                      onChange={(e) => setCustomerForm({...customerForm, email: e.target.value})}
                      placeholder="البريد الإلكتروني"
                    />
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button onClick={handleAddCustomer} className="flex-1">
                      إضافة العميل
                    </Button>
                    <Button variant="outline" onClick={() => setIsCustomerDialogOpen(false)}>
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {customers.map((customer) => (
              <Card key={customer.id} className="card-hover">
                <CardHeader>
                  <CardTitle className="text-lg">{customer.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">الهاتف:</span>
                      <span>{customer.phone}</span>
                    </div>
                    {customer.email && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">البريد:</span>
                        <span className="text-sm">{customer.email}</span>
                      </div>
                    )}
                    {customer.address && (
                      <div className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        {customer.address}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {customers.length === 0 && (
            <Card>
              <CardContent className="py-12 text-center">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <div className="text-gray-500 text-lg mb-4">لا توجد عملاء مسجلين</div>
                <Button onClick={() => setIsCustomerDialogOpen(true)} className="gap-2">
                  <Plus className="w-4 h-4" />
                  إضافة أول عميل
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="suppliers" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">إدارة الموردين</h2>
            <Dialog open={isSupplierDialogOpen} onOpenChange={setIsSupplierDialogOpen}>
              <DialogTrigger asChild>
                <Button className="gap-2">
                  <Plus className="w-4 h-4" />
                  إضافة مورد جديد
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>إضافة مورد جديد</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="supplierName">اسم المورد *</Label>
                    <Input
                      id="supplierName"
                      value={supplierForm.name}
                      onChange={(e) => setSupplierForm({...supplierForm, name: e.target.value})}
                      placeholder="اسم المورد"
                    />
                  </div>
                  <div>
                    <Label htmlFor="supplierPhone">رقم الهاتف *</Label>
                    <Input
                      id="supplierPhone"
                      value={supplierForm.phone}
                      onChange={(e) => setSupplierForm({...supplierForm, phone: e.target.value})}
                      placeholder="رقم الهاتف"
                    />
                  </div>
                  <div>
                    <Label htmlFor="supplierAddress">العنوان</Label>
                    <Textarea
                      id="supplierAddress"
                      value={supplierForm.address}
                      onChange={(e) => setSupplierForm({...supplierForm, address: e.target.value})}
                      placeholder="العنوان"
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="supplierEmail">البريد الإلكتروني</Label>
                    <Input
                      id="supplierEmail"
                      type="email"
                      value={supplierForm.email}
                      onChange={(e) => setSupplierForm({...supplierForm, email: e.target.value})}
                      placeholder="البريد الإلكتروني"
                    />
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button onClick={handleAddSupplier} className="flex-1">
                      إضافة المورد
                    </Button>
                    <Button variant="outline" onClick={() => setIsSupplierDialogOpen(false)}>
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {suppliers.map((supplier) => (
              <Card key={supplier.id} className="card-hover">
                <CardHeader>
                  <CardTitle className="text-lg">{supplier.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">الهاتف:</span>
                      <span>{supplier.phone}</span>
                    </div>
                    {supplier.email && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">البريد:</span>
                        <span className="text-sm">{supplier.email}</span>
                      </div>
                    )}
                    {supplier.address && (
                      <div className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        {supplier.address}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {suppliers.length === 0 && (
            <Card>
              <CardContent className="py-12 text-center">
                <Truck className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <div className="text-gray-500 text-lg mb-4">لا توجد موردين مسجلين</div>
                <Button onClick={() => setIsSupplierDialogOpen(true)} className="gap-2">
                  <Plus className="w-4 h-4" />
                  إضافة أول مورد
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="debts" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">إدارة المديونيات والمستحقات</h2>
            <Dialog open={isDebtDialogOpen} onOpenChange={setIsDebtDialogOpen}>
              <DialogTrigger asChild>
                <Button className="gap-2">
                  <Plus className="w-4 h-4" />
                  إضافة مديونية جديدة
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>إضافة مديونية جديدة</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="debtType">نوع المديونية</Label>
                    <Select value={debtForm.type} onValueChange={(value: 'customer' | 'supplier') => setDebtForm({...debtForm, type: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر نوع المديونية" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="customer">مديونية عميل</SelectItem>
                        <SelectItem value="supplier">مستحق لمورد</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {debtForm.type === 'customer' && (
                    <div>
                      <Label htmlFor="customerId">العميل</Label>
                      <Select value={debtForm.customerId} onValueChange={(value) => setDebtForm({...debtForm, customerId: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر العميل" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {debtForm.type === 'supplier' && (
                    <div>
                      <Label htmlFor="supplierId">المورد</Label>
                      <Select value={debtForm.supplierId} onValueChange={(value) => setDebtForm({...debtForm, supplierId: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر المورد" />
                        </SelectTrigger>
                        <SelectContent>
                          {suppliers.map((supplier) => (
                            <SelectItem key={supplier.id} value={supplier.id}>
                              {supplier.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div>
                    <Label htmlFor="debtAmount">المبلغ *</Label>
                    <Input
                      id="debtAmount"
                      type="number"
                      step="0.01"
                      value={debtForm.amount}
                      onChange={(e) => setDebtForm({...debtForm, amount: e.target.value})}
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <Label htmlFor="debtDescription">الوصف *</Label>
                    <Textarea
                      id="debtDescription"
                      value={debtForm.description}
                      onChange={(e) => setDebtForm({...debtForm, description: e.target.value})}
                      placeholder="وصف المديونية"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="dueDate">تاريخ الاستحقاق</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={debtForm.dueDate}
                      onChange={(e) => setDebtForm({...debtForm, dueDate: e.target.value})}
                    />
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button onClick={handleAddDebt} className="flex-1">
                      إضافة المديونية
                    </Button>
                    <Button variant="outline" onClick={() => setIsDebtDialogOpen(false)}>
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="space-y-4">
            {debts.map((debt) => (
              <Card key={debt.id} className="card-hover">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">
                        {debt.type === 'customer' ? debt.customerName : debt.supplierName}
                      </CardTitle>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {debt.type === 'customer' ? 'مديونية عميل' : 'مستحق لمورد'}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-red-600">
                        {getRemainingDebtAmount(debt.id).toFixed(2)} ج.م
                      </div>
                      {getStatusBadge(debt.status)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-gray-700 dark:text-gray-300">
                      {debt.description}
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">المبلغ الأصلي:</span>
                        <span className="font-medium ml-2">{debt.amount.toFixed(2)} ج.م</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">المبلغ المتبقي:</span>
                        <span className="font-medium ml-2">{getRemainingDebtAmount(debt.id).toFixed(2)} ج.م</span>
                      </div>
                      {debt.dueDate && (
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">تاريخ الاستحقاق:</span>
                          <span className="font-medium ml-2">
                            {new Date(debt.dueDate).toLocaleDateString('ar-EG')}
                          </span>
                        </div>
                      )}
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">تاريخ الإنشاء:</span>
                        <span className="font-medium ml-2">
                          {new Date(debt.createdAt).toLocaleDateString('ar-EG')}
                        </span>
                      </div>
                    </div>

                    {debt.status !== 'paid' && (
                      <div className="flex gap-2 pt-3">
                        <Button
                          size="sm"
                          onClick={() => {
                            setSelectedDebt(debt);
                            setIsPaymentDialogOpen(true);
                          }}
                          className="gap-2"
                        >
                          <DollarSign className="w-4 h-4" />
                          تسجيل دفعة
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => deleteDebt(debt.id)}
                          className="gap-2"
                        >
                          <Trash className="w-4 h-4" />
                          حذف
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {debts.length === 0 && (
            <Card>
              <CardContent className="py-12 text-center">
                <CreditCard className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <div className="text-gray-500 text-lg mb-4">لا توجد مديونيات مسجلة</div>
                <Button onClick={() => setIsDebtDialogOpen(true)} className="gap-2">
                  <Plus className="w-4 h-4" />
                  إضافة أول مديونية
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Payment Dialog */}
        <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تسجيل دفعة</DialogTitle>
            </DialogHeader>
            {selectedDebt && (
              <div className="space-y-4">
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <div className="font-medium">
                    {selectedDebt.type === 'customer' ? selectedDebt.customerName : selectedDebt.supplierName}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    المبلغ المتبقي: {getRemainingDebtAmount(selectedDebt.id).toFixed(2)} ج.م
                  </div>
                </div>

                <div>
                  <Label htmlFor="paymentAmount">مبلغ الدفعة *</Label>
                  <Input
                    id="paymentAmount"
                    type="number"
                    step="0.01"
                    value={paymentForm.amount}
                    onChange={(e) => setPaymentForm({...paymentForm, amount: e.target.value})}
                    placeholder="0.00"
                    max={getRemainingDebtAmount(selectedDebt.id)}
                  />
                </div>

                <div>
                  <Label htmlFor="paymentMethod">طريقة الدفع</Label>
                  <Select value={paymentForm.paymentMethod} onValueChange={(value: 'cash' | 'bank' | 'check') => setPaymentForm({...paymentForm, paymentMethod: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cash">نقداً</SelectItem>
                      <SelectItem value="bank">تحويل بنكي</SelectItem>
                      <SelectItem value="check">شيك</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="paymentNotes">ملاحظات</Label>
                  <Textarea
                    id="paymentNotes"
                    value={paymentForm.notes}
                    onChange={(e) => setPaymentForm({...paymentForm, notes: e.target.value})}
                    placeholder="ملاحظات إضافية"
                    rows={3}
                  />
                </div>

                <div className="flex gap-2 pt-4">
                  <Button onClick={handleAddPayment} className="flex-1">
                    تسجيل الدفعة
                  </Button>
                  <Button variant="outline" onClick={() => setIsPaymentDialogOpen(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </Tabs>
    </div>
  );
};

export default Debts;
