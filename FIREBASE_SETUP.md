# 🔥 دليل إعداد Firebase لنظام إدارة المحل

## نظرة عامة

هذا الدليل يوضح كيفية ربط نظام إدارة المحل بقاعدة بيانات Firebase للحصول على:
- ✅ حفظ البيانات في السحابة
- ✅ مزامنة فورية للبيانات
- ✅ نسخ احتياطي تلقائي
- ✅ الوصول من أي جهاز
- ✅ أمان عالي

## الخطوات المطلوبة

### 1. إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. انقر على "إضافة مشروع" أو "Add project"
3. أدخل اسم المشروع (مثل: `masrafy-shop-manager`)
4. اختر إعدادات المشروع حسب تفضيلك
5. انقر على "إنشاء المشروع"

### 2. إضافة تطبيق ويب

1. في لوحة تحكم المشروع، انقر على أيقونة الويب `</>`
2. أدخل اسم التطبيق (مثل: `Masrafy Shop Manager`)
3. فعل "Firebase Hosting" إذا كنت تريد استضافة التطبيق
4. انقر على "تسجيل التطبيق"

### 3. إعداد Firestore Database

1. في القائمة الجانبية، انقر على "Firestore Database"
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. اختر موقع قاعدة البيانات (يفضل أقرب منطقة جغرافية)

### 4. إعداد Authentication

1. في القائمة الجانبية، انقر على "Authentication"
2. انقر على "البدء"
3. في تبويب "Sign-in method"، فعل "Email/Password"
4. احفظ الإعدادات

### 5. نسخ إعدادات التكوين

1. اذهب إلى إعدادات المشروع (⚙️)
2. في قسم "Your apps"، انقر على تطبيق الويب
3. انسخ كود التكوين
4. استبدل القيم في ملف `src/lib/firebase.ts`

```typescript
// src/lib/firebase.ts
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_PROJECT_ID.appspot.com",
  messagingSenderId: "YOUR_SENDER_ID",
  appId: "YOUR_APP_ID"
};

const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const auth = getAuth(app);
export default app;
```

### 6. إعداد قواعد الأمان

في Firestore Database، اذهب إلى تبويب "Rules" وأضف القواعد التالية:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to all documents
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

⚠️ **تحذير**: هذه القواعد للتطوير فقط. في الإنتاج، يجب إضافة قواعد أمان أكثر تقييداً.

## Collections المطلوبة

سيتم إنشاء هذه المجموعات تلقائياً عند إضافة البيانات:

- `products` - المنتجات
- `sales` - المبيعات
- `customers` - العملاء
- `suppliers` - الموردين
- `debts` - المديونيات
- `debtPayments` - دفعات المديونيات

## الميزات المتاحة بعد الإعداد

### 🔄 مزامنة فورية
- تحديث البيانات في الوقت الفعلي
- رؤية التغييرات فوراً على جميع الأجهزة

### 💾 نسخ احتياطي تلقائي
- حفظ تلقائي لجميع البيانات
- استعادة البيانات في حالة فقدانها

### 🌐 الوصول من أي مكان
- تسجيل الدخول من أي جهاز
- الوصول للبيانات من أي متصفح

### 🔒 أمان متقدم
- تشفير البيانات
- حماية من الوصول غير المصرح

## استكشاف الأخطاء

### خطأ في الاتصال
```
Error: Firebase configuration not found
```
**الحل**: تأكد من إضافة إعدادات Firebase الصحيحة في `src/lib/firebase.ts`

### خطأ في الصلاحيات
```
Error: Missing or insufficient permissions
```
**الحل**: تحقق من قواعد Firestore وتأكد من السماح بالقراءة والكتابة

### خطأ في المصادقة
```
Error: Authentication failed
```
**الحل**: تأكد من تفعيل Email/Password في Authentication

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من [وثائق Firebase](https://firebase.google.com/docs)
2. راجع [أمثلة Firebase](https://github.com/firebase/quickstart-js)
3. تحقق من console المتصفح للأخطاء

## الخطوات التالية

بعد إعداد Firebase بنجاح:
1. اختبر إضافة منتج جديد
2. تحقق من ظهور البيانات في Firebase Console
3. جرب الوصول من جهاز آخر
4. اختبر المزامنة الفورية

---

🎉 **مبروك!** أصبح لديك نظام إدارة محل متكامل مع قاعدة بيانات سحابية!
